package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

type BalancePoint struct {
	Timestamp int64 `json:"timestamp7"`
	Balance   int64 `json:"balance"`
}

// TokenBalanceConfig holds configuration for token balance calculation
type TokenBalanceConfig struct {
	FromSlot uint64 // Starting slot number
	RPCURL   string // RPC endpoint URL (optional, defaults to mainnet)
}

// TokenBalanceResult holds the result of token balance calculation
type TokenBalanceResult struct {
	BalancePoints       []BalancePoint
	TotalCurrentBalance int64
	NumIntervals        uint64
}

func getCurrentTokenBalance(ctx context.Context, client *rpc.Client, wallet solana.PublicKey, tokenMint solana.PublicKey) (uint64, error) {
	// Find the token account for this wallet and mint
	tokenAccounts, err := client.GetTokenAccountsByOwner(
		ctx,
		wallet,
		&rpc.GetTokenAccountsConfig{
			Mint: &tokenMint,
		},
		&rpc.GetTokenAccountsOpts{
			Encoding: solana.EncodingBase64,
		},
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token accounts: %v", err)
	}

	if len(tokenAccounts.Value) == 0 {
		return 0, nil
	}

	// Get the token account data
	accountInfo, err := client.GetTokenAccountBalance(
		ctx,
		tokenAccounts.Value[0].Pubkey,
		rpc.CommitmentConfirmed,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token account balance: %v", err)
	}

	return strconv.ParseUint(accountInfo.Value.Amount, 10, 64)
}

func readWalletsFromFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open wallet file: %v", err)
	}
	defer file.Close()

	var wallets []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		wallet := strings.TrimSpace(scanner.Text())
		if wallet != "" {
			wallets = append(wallets, wallet)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading wallet file: %v", err)
	}

	return wallets, nil
}

// CalculateTokenBalanceHistory calculates historical token balance points for given wallets and token mint.
func CalculateTokenBalanceHistory(ctx context.Context, tokenMint string, wallets []string, config TokenBalanceConfig) (*TokenBalanceResult, error) {
	// Validate inputs
	if tokenMint == "" {
		return nil, fmt.Errorf("token mint address is required")
	}
	if len(wallets) == 0 {
		return nil, fmt.Errorf("at least one wallet address is required")
	}
	if config.FromSlot == 0 {
		return nil, fmt.Errorf("from-slot must be greater than 0")
	}

	// Parse token mint
	tokenMintPubkey, err := solana.PublicKeyFromBase58(tokenMint)
	if err != nil {
		return nil, fmt.Errorf("invalid token mint address: %v", err)
	}

	// Parse wallet addresses
	var walletPubkeys []solana.PublicKey
	for _, walletStr := range wallets {
		walletPubkey, err := solana.PublicKeyFromBase58(walletStr)
		if err != nil {
			return nil, fmt.Errorf("invalid wallet address %s: %v", walletStr, err)
		}
		walletPubkeys = append(walletPubkeys, walletPubkey)
	}

	// Set up RPC client
	rpcURL := config.RPCURL
	if rpcURL == "" {
		rpcURL = os.Getenv("RPC_URL")
		if rpcURL == "" {
			rpcURL = "https://api.mainnet-beta.solana.com"
		}
	}
	client := rpc.New(rpcURL)

	// Get current slot
	currentSlot, err := client.GetSlot(ctx, rpc.CommitmentConfirmed)
	if err != nil {
		return nil, fmt.Errorf("failed to get current slot: %v", err)
	}

	// Calculate number of intervals (1500 slots per interval)
	numIntervals := (currentSlot - config.FromSlot) / 1500
	if numIntervals == 0 {
		numIntervals = 1
	}

	// Get current total balance across all wallets
	var totalCurrentBalance int64
	for _, walletPubkey := range walletPubkeys {
		balance, err := getCurrentTokenBalance(ctx, client, walletPubkey, tokenMintPubkey)
		if err != nil {
			return nil, fmt.Errorf("failed to get current balance for wallet %s: %v", walletPubkey.String(), err)
		}
		totalCurrentBalance += int64(balance)
	}

	// Convert wallet and token mint to bytes for database queries
	var walletBytes [][]byte
	for _, wallet := range walletPubkeys {
		walletBytes = append(walletBytes, wallet.Bytes())
	}
	tokenMintBytes := tokenMintPubkey.Bytes()

	// Initialize balance points array
	balancePoints := make([]BalancePoint, numIntervals)
	currentBalance := totalCurrentBalance

	// Calculate balance points working backwards from current slot
	for i := uint64(0); i < numIntervals; i++ {
		endSlot := currentSlot - (i * 1500)
		startSlot := endSlot - 1500 + 1
		if startSlot < config.FromSlot {
			startSlot = config.FromSlot
		}

		// Get balance changes from database
		changes, err := db.Get().GetTokenBalanceChanges(ctx, walletBytes, tokenMintBytes, startSlot, endSlot)
		if err != nil {
			return nil, fmt.Errorf("failed to get balance changes for interval %d: %v", i, err)
		}

		// Calculate balance at this point
		currentBalance -= changes

		// Get block time
		blockTime, err := client.GetBlockTime(ctx, endSlot)
		if err != nil {
			fmt.Printf("Warning: Failed to get block time for slot %d: %v\n", endSlot, err)
			continue
		}

		balancePoints[i] = BalancePoint{
			Timestamp: int64(*blockTime),
			Balance:   currentBalance,
		}
	}

	// Reverse the array to get chronological order (oldest to newest)
	for i, j := 0, len(balancePoints)-1; i < j; i, j = i+1, j-1 {
		balancePoints[i], balancePoints[j] = balancePoints[j], balancePoints[i]
	}

	return &TokenBalanceResult{
		BalancePoints:       balancePoints,
		TotalCurrentBalance: totalCurrentBalance,
		NumIntervals:        numIntervals,
	}, nil
}

func main() {
	// Parse command line arguments
	walletsStr := flag.String("wallets", "", "Comma-separated list of wallet addresses")
	walletsFile := flag.String("wallets-file", "", "Path to file containing wallet addresses (one per line)")
	tokenMintStr := flag.String("token-mint", "", "Token mint address")
	fromSlot := flag.Uint64("from-slot", 0, "Starting slot number")
	outputFile := flag.String("output", "balance_points.json", "Output JSON file path")
	flag.Parse()

	if (*walletsStr == "" && *walletsFile == "") || *tokenMintStr == "" || *fromSlot == 0 {
		log.Fatal("either wallets or wallets-file, token-mint, and from-slot are required")
	}

	// Initialize logger
	ctx := context.Background()
	if err := logger.Initialize(ctx, "token-balance"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	// Initialize database
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Get wallet addresses
	var walletStrs []string
	var err error
	if *walletsFile != "" {
		walletStrs, err = readWalletsFromFile(*walletsFile)
		if err != nil {
			log.Fatalf("Failed to read wallets from file: %v", err)
		}
	} else {
		walletStrs = strings.Split(*walletsStr, ",")
	}

	// Create configuration
	config := TokenBalanceConfig{
		FromSlot: *fromSlot,
		RPCURL:   "", // Will use environment variable or default
	}

	// Call the reusable function
	libResult, err := CalculateTokenBalanceHistory(ctx, *tokenMintStr, walletStrs, config)
	if err != nil {
		log.Fatalf("Failed to calculate token balance history: %v", err)
	}

	log.Printf("totalCurrentBalance: %d, numIntervals: %d", libResult.TotalCurrentBalance, libResult.NumIntervals)

	// Write to JSON file
	file, err := os.Create(*outputFile)
	if err != nil {
		log.Fatalf("Failed to create output file: %v", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(libResult.BalancePoints); err != nil {
		log.Fatalf("Failed to encode balance points: %v", err)
	}
	log.Printf("Successfully wrote balance points to %s", *outputFile)
}
