package main

/*
#include <stdlib.h>
#include <string.h>
*/
import "C"

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"sync"
	"unsafe"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/mr-tron/base58"
)

var (
	initOnce sync.Once
	initErr  error
)

// BalancePoint represents a balance point in time
type BalancePoint struct {
	Timestamp int64 `json:"timestamp"`
	Balance   int64 `json:"balance"`
}

// TokenBalanceConfig holds configuration for token balance calculation
type TokenBalanceConfig struct {
	FromSlot uint64 // Starting slot number
	RPCURL   string // RPC endpoint URL (optional, defaults to mainnet)
}

// TokenBalanceResult holds the result of token balance calculation
type TokenBalanceResult struct {
	BalancePoints       []BalancePoint
	TotalCurrentBalance int64
	NumIntervals        uint64
}

// FFITokenBalanceConfig represents the configuration for FFI calls
type FFITokenBalanceConfig struct {
	FromSlot uint64 `json:"from_slot"`
	RPCURL   string `json:"rpc_url"`
}

// FFITokenBalanceResult represents the result returned via FFI
type FFITokenBalanceResult struct {
	BalancePoints       []BalancePoint `json:"balance_points"`
	TotalCurrentBalance int64          `json:"total_current_balance"`
	NumIntervals        uint64         `json:"num_intervals"`
	Error               string         `json:"error,omitempty"`
}

func getCurrentTokenBalance(ctx context.Context, client *rpc.Client, wallet solana.PublicKey, tokenMint solana.PublicKey) (uint64, error) {
	// Find the token account for this wallet and mint
	tokenAccounts, err := client.GetTokenAccountsByOwner(
		ctx,
		wallet,
		&rpc.GetTokenAccountsConfig{
			Mint: &tokenMint,
		},
		&rpc.GetTokenAccountsOpts{
			Encoding: solana.EncodingBase64,
		},
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token accounts: %v", err)
	}

	if len(tokenAccounts.Value) == 0 {
		return 0, nil
	}

	// Get the token account data
	accountInfo, err := client.GetTokenAccountBalance(
		ctx,
		tokenAccounts.Value[0].Pubkey,
		rpc.CommitmentConfirmed,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token account balance: %v", err)
	}

	return strconv.ParseUint(accountInfo.Value.Amount, 10, 64)
}

// CalculateTokenBalanceHistory calculates historical token balance points for given wallets and token mint.
func CalculateTokenBalanceHistory(ctx context.Context, tokenMint string, wallets []string, config TokenBalanceConfig) (*TokenBalanceResult, error) {
	// Validate inputs
	if tokenMint == "" {
		return nil, fmt.Errorf("token mint address is required")
	}
	if len(wallets) == 0 {
		return nil, fmt.Errorf("at least one wallet address is required")
	}
	if config.FromSlot == 0 {
		return nil, fmt.Errorf("from-slot must be greater than 0")
	}

	// Parse and validate wallet addresses
	walletBytes := make([][]byte, len(wallets))
	solanaWallets := make([]solana.PublicKey, len(wallets))
	for i, w := range wallets {
		decoded, err := base58.Decode(strings.TrimSpace(w))
		if err != nil {
			return nil, fmt.Errorf("failed to decode wallet address %s: %v", w, err)
		}
		walletBytes[i] = decoded
		solanaWallets[i] = solana.PublicKeyFromBytes(decoded)
	}

	// Parse token mint
	tokenMintBytes, err := base58.Decode(tokenMint)
	if err != nil {
		return nil, fmt.Errorf("failed to decode token mint address: %v", err)
	}
	solanaTokenMint := solana.PublicKeyFromBytes(tokenMintBytes)

	// Set up RPC URL
	rpcURL := config.RPCURL
	if rpcURL == "" {
		rpcURL = os.Getenv("RPC_URL")
		if rpcURL == "" {
			rpcURL = "https://solana-mainnet.g.alchemy.com/v2/********************************"
		}
	}

	// Initialize RPC client
	client := rpc.New(rpcURL)

	// Get current slot from RPC
	currentSlot, err := client.GetSlot(ctx, rpc.CommitmentFinalized)
	if err != nil {
		return nil, fmt.Errorf("failed to get current slot: %v", err)
	}

	// Calculate number of intervals
	slotRange := currentSlot - config.FromSlot
	numIntervals := slotRange / 1500
	if slotRange%1500 != 0 {
		numIntervals++
	}

	// Get current balances for all wallets
	var totalCurrentBalance int64
	for _, wallet := range solanaWallets {
		balance, err := getCurrentTokenBalance(ctx, client, wallet, solanaTokenMint)
		if err != nil {
			fmt.Printf("Warning: Failed to get current balance for wallet %s: %v\n", wallet.String(), err)
			continue
		}
		totalCurrentBalance += int64(balance)
	}

	// Generate balance points
	balancePoints := make([]BalancePoint, numIntervals)
	currentBalance := totalCurrentBalance

	for i := uint64(0); i < numIntervals; i++ {
		endSlot := currentSlot - (i * 1500)
		startSlot := endSlot - 1500 + 1
		if startSlot < config.FromSlot {
			startSlot = config.FromSlot
		}

		// Get balance changes from database
		changes, err := db.Get().GetTokenBalanceChanges(ctx, walletBytes, tokenMintBytes, startSlot, endSlot)
		if err != nil {
			return nil, fmt.Errorf("failed to get balance changes for interval %d: %v", i, err)
		}

		// Calculate balance at this point
		currentBalance -= changes

		// Get block time
		blockTime, err := client.GetBlockTime(ctx, endSlot)
		if err != nil {
			fmt.Printf("Warning: Failed to get block time for slot %d: %v\n", endSlot, err)
			continue
		}

		balancePoints[i] = BalancePoint{
			Timestamp: int64(*blockTime),
			Balance:   currentBalance,
		}
	}

	// Reverse the array to get chronological order
	for i, j := 0, len(balancePoints)-1; i < j; i, j = i+1, j-1 {
		balancePoints[i], balancePoints[j] = balancePoints[j], balancePoints[i]
	}

	return &TokenBalanceResult{
		BalancePoints:       balancePoints,
		TotalCurrentBalance: totalCurrentBalance,
		NumIntervals:        numIntervals,
	}, nil
}

// initializeOnce initializes the logger and database connections once
func initializeOnce() {
	initOnce.Do(func() {
		ctx := context.Background()

		// Initialize logger
		if err := logger.Initialize(ctx, "token-balance-ffi"); err != nil {
			initErr = fmt.Errorf("failed to initialize logger: %v", err)
			return
		}

		// Initialize database
		if err := db.Initialize(); err != nil {
			initErr = fmt.Errorf("failed to initialize database: %v", err)
			return
		}
	})
}

//export InitializeTokenBalance
func InitializeTokenBalance() *C.char {
	initializeOnce()
	if initErr != nil {
		result := map[string]string{"error": initErr.Error()}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	result := map[string]string{"status": "success"}
	jsonBytes, _ := json.Marshal(result)
	return C.CString(string(jsonBytes))
}

//export CalculateTokenBalanceHistoryFFI
func CalculateTokenBalanceHistoryFFI(tokenMint *C.char, walletsJSON *C.char, configJSON *C.char) *C.char {
	// Ensure initialization
	initializeOnce()
	if initErr != nil {
		result := FFITokenBalanceResult{Error: initErr.Error()}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	// Convert C strings to Go strings
	goTokenMint := C.GoString(tokenMint)
	goWalletsJSON := C.GoString(walletsJSON)
	goConfigJSON := C.GoString(configJSON)

	// Parse wallets JSON array
	var wallets []string
	if err := json.Unmarshal([]byte(goWalletsJSON), &wallets); err != nil {
		result := FFITokenBalanceResult{Error: fmt.Sprintf("failed to parse wallets JSON: %v", err)}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	// Parse config JSON
	var config FFITokenBalanceConfig
	if err := json.Unmarshal([]byte(goConfigJSON), &config); err != nil {
		result := FFITokenBalanceResult{Error: fmt.Sprintf("failed to parse config JSON: %v", err)}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	// Convert to internal config format
	internalConfig := TokenBalanceConfig{
		FromSlot: config.FromSlot,
		RPCURL:   config.RPCURL,
	}

	// Multiple ways to ensure debug output is visible
	fmt.Printf("FFI: Calculating balance history for token %s from slot %d for wallets: %v\n", goTokenMint, config.FromSlot, wallets)

	// Also use log package which might be more reliable in shared library context
	log.Printf("FFI: Starting calculation for token %s with %d wallets\n", goTokenMint, len(wallets))

	// Write to stderr as well (sometimes more reliable than stdout)
	fmt.Fprintf(os.Stderr, "FFI: Debug - Processing token %s\n", goTokenMint)
	// Call the actual function
	ctx := context.Background()
	os.Stdout.Sync()
	os.Stderr.Sync()

	result, err := CalculateTokenBalanceHistory(ctx, goTokenMint, wallets, internalConfig)
	if err != nil {
		ffiResult := FFITokenBalanceResult{Error: err.Error()}
		jsonBytes, _ := json.Marshal(ffiResult)
		return C.CString(string(jsonBytes))
	}

	// Convert result to FFI format
	ffiResult := FFITokenBalanceResult{
		BalancePoints:       result.BalancePoints,
		TotalCurrentBalance: result.TotalCurrentBalance,
		NumIntervals:        result.NumIntervals,
	}

	// Marshal to JSON
	jsonBytes, err := json.Marshal(ffiResult)
	if err != nil {
		errorResult := FFITokenBalanceResult{Error: fmt.Sprintf("failed to marshal result: %v", err)}
		errorBytes, _ := json.Marshal(errorResult)
		return C.CString(string(errorBytes))
	}

	return C.CString(string(jsonBytes))
}

//export FreeString
func FreeString(str *C.char) {
	C.free(unsafe.Pointer(str))
}

//export CleanupTokenBalance
func CleanupTokenBalance() {
	if err := logger.Close(); err != nil {
		fmt.Printf("Error closing logger: %v\n", err)
	}
	if err := db.Close(); err != nil {
		fmt.Printf("Error closing database: %v\n", err)
	}
}

//export CalculateTokenBalanceHistorySimple
func CalculateTokenBalanceHistorySimple(tokenMint *C.char, walletsCSV *C.char, fromSlot C.ulonglong, rpcURL *C.char) *C.char {
	// Convert inputs
	goTokenMint := C.GoString(tokenMint)
	goWalletsCSV := C.GoString(walletsCSV)
	goRPCURL := C.GoString(rpcURL)

	// Parse wallets from CSV
	wallets := strings.Split(goWalletsCSV, ",")
	for i, wallet := range wallets {
		wallets[i] = strings.TrimSpace(wallet)
	}

	// Create config
	config := FFITokenBalanceConfig{
		FromSlot: uint64(fromSlot),
		RPCURL:   goRPCURL,
	}

	// Convert to JSON and call main function
	walletsJSON, _ := json.Marshal(wallets)
	configJSON, _ := json.Marshal(config)

	return CalculateTokenBalanceHistoryFFI(
		C.CString(goTokenMint),
		C.CString(string(walletsJSON)),
		C.CString(string(configJSON)),
	)
}

func main() {
	// Empty main function for shared library build
}
